<template>
  <div :class="[b(), className]" :style="styleSizeName" @mouseover="handleMouseOver" @mouseleave="handleMouseLeave">
    <el-table class="custom-table-header" :class="option.roundedTable?'rounded-table':''" :style="styleChartName" ref="table" @cell-click="cellClick" :data="dataChart" :height="height"
      :border="option.border" :cellStyle="cellStyle" :row-style="rowStyle" :show-header="showHeader"
      :header-row-style="headerRowStyle" :header-cell-style="headerCellStyle" :span-method="option.enableMergeColumn ? spanMethod : null">
     
      <el-table-column type="index" label="#" header-align="center" align="center" v-if="option.index"
        :width="!!option.indexWidth ? option.indexWidth : 80">
        <div :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px"}' slot-scope="{$index}">{{ $index + 1 }}</div>
      </el-table-column>
      <template v-for="(item, index) in option.column">
        <el-table-column v-if="item.hide !== true" show-overflow-tooltip :key='index' :prop="item.prop"
          :label="item.label" :width="item.width">
          <template slot-scope="{row}">
            <template v-if="item.wordBreak == true">
               <!-- NEW 单元格自动换行 -->
                <div  v-html="getFormatter(item, row)" v-if="item.formatter && reload"></div>
                <div class="cell-word-break" :style='{ color: getColor(item, row), background: getbackColor(item, row) }' v-else>
                    <!--隐藏字段，无法作为判断，但可以设置宽为1隐藏列代替-->
                  <span v-if="!(item?.width =='1')"> {{ row[item.prop] }}</span>
                </div>
            </template>
            <template v-else>
              <div  :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px"}' v-html="getFormatter(item, row)" v-if="item.formatter && reload"></div>
                <div  :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px", color: getColor(item, row), background: getbackColor(item, row) }' v-else>
                    <!--隐藏字段，无法作为判断，但可以设置宽为1隐藏列代替-->
                  <span v-if="!(item?.width =='1')"> {{ row[item.prop] }}</span>
                </div>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div v-if="tableCellStyle" v-html="tableCellStyle"></div>
  </div>
</template>

<script>
import create from "../../create";
import { getFunction } from '@/utils/utils';
export default create({
  name: "table",
  data() {
    return {
      tableCellStyle:null,
      reload: true,
      headerHeight: '',
      scrollCheck: null,

    };
  },
  watch: {
    'option.column'() {
      var num = 0
      num++
      this.reload = false
      this.$nextTick(() => {
        this.reload = true

      })
      setTimeout(() => {

        this.headerHeight = this.headerHeight + num
      }, 600);
    },
    scrollSpeed() {
      this.setTime();
    },
    scroll: {
      handler() {
        this.setTime();
      },
    },
    // dataChart:{
    //   handler(n,o){
    //      if(n){
    //       console.log("dataChart change#######################:",n)
    //      }
    //   },
    //   deep:true
    // }

  },
  computed: {
    showHeader() {
      return this.option.showHeader
    },
    scrollTime() {
      return this.option.scrollTime
    },
    scrollSpeed() {
      return this.option.scrollSpeed || 1
    },
    scroll() {
      return this.option.scroll
    },
    cellHeight() {
      return parseInt((this.height - this.headerHeight) / this.option.count)
    }
  },
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  mounted() {
  },
  created() {
    this.$nextTick(() => {
      this.headerHeight = this.$refs.table.$refs.headerWrapper ? parseInt(this.$refs.table.$refs.headerWrapper.clientHeight) : 0
      setTimeout(async() => {
        await this.setTime();
        //滚动间隔
      }, this.scrollTime)
    })
  },
  methods: {
    // 合并列方法
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!this.option.enableMergeColumn) {
        return [1, 1];
      }

      // 获取当前列的配置
      const currentColumn = this.option.column.find(col => col.prop === column.property);
      if (!currentColumn || !currentColumn.mergeColumn) {
        return [1, 1];
      }

      // 计算合并的行数
      const prop = column.property;
      const currentValue = row[prop];

      // 如果当前值为空或undefined，不合并
      if (currentValue === null || currentValue === undefined || currentValue === '') {
        return [1, 1];
      }

      // 查找连续相同值的行数
      let rowspan = 1;
      let colspan = 1;

      // 向下查找相同值
      for (let i = rowIndex + 1; i < this.dataChart.length; i++) {
        if (this.dataChart[i][prop] === currentValue) {
          rowspan++;
        } else {
          break;
        }
      }

      // 向上查找，如果上一行有相同值，则当前行不显示（rowspan = 0）
      for (let i = rowIndex - 1; i >= 0; i--) {
        if (this.dataChart[i][prop] === currentValue) {
          return [0, 0]; // 不显示当前单元格
        } else {
          break;
        }
      }

      return [rowspan, colspan];
    },

    getColor(item, row) {
      try {
        // 验证输入参数
        if (!this.validateConditionParams(item, row)) {
          return null
        }

        // 使用统一的多条件处理逻辑
        return this.processAllConditions(item, row, 'cellfont')
      } catch (error) {
        console.error('获取字体颜色出错:', error, { item, row })
        return null
      }
    },
    // 统一处理所有条件（主条件 + 多条件）
    processAllConditions(item, row, typeName = 'cellbackground') {
      let finalColor = null
      let appliedStyles = {
        rowbackground: null,
        rowfont: null,
        cellfont: null,
        cellbackground: null
      }

      try {
        // 收集所有条件（主条件 + 扩展条件）
        const allConditions = []

        // 1. 添加主条件
        if (item.condition && item.value !== undefined && item.value !== null) {
          allConditions.push({
            ...item,
            conditionIndex: 0,
            conditionType: '主条件'
          })
        }

        // 2. 添加扩展条件
        if (item.editableTabsFormJSON && item.editableTabsFormJSON.length > 0) {
          item.editableTabsFormJSON.forEach((tabItem, index) => {
            if (tabItem.condition && tabItem.value !== undefined && tabItem.value !== null) {
              allConditions.push({
                ...tabItem,
                prop: item.prop, // 确保使用相同的字段
                conditionIndex: index + 1,
                conditionType: `扩展条件${index + 1}`
              })
            }
          })
        }

        console.log(`🔍 处理列 ${item.prop} 的所有条件:`, {
          totalConditions: allConditions.length,
          conditions: allConditions.map(c => ({
            type: c.conditionType,
            condition: c.condition,
            value: c.value
          }))
        })

        // 3. 按顺序检查所有条件，后面的条件优先级更高
        allConditions.forEach((conditionItem) => {
          const currentColor = this.getColorByType(conditionItem, row, typeName)

          if (currentColor) {
            // 如果当前条件满足，更新最终颜色和样式
            finalColor = currentColor

            // 记录当前条件应用的样式
            if (conditionItem.rowbackground) appliedStyles.rowbackground = conditionItem.rowbackground
            if (conditionItem.rowfont) appliedStyles.rowfont = conditionItem.rowfont
            if (conditionItem.cellfont) appliedStyles.cellfont = conditionItem.cellfont
            if (conditionItem.cellbackground) appliedStyles.cellbackground = conditionItem.cellbackground

            console.log(`✅ ${conditionItem.conditionType}满足:`, {
              condition: conditionItem.condition,
              value: conditionItem.value,
              cellValue: row[conditionItem.prop],
              appliedColor: currentColor,
              typeName: typeName
            })
          } else {
            console.log(`❌ ${conditionItem.conditionType}不满足:`, {
              condition: conditionItem.condition,
              value: conditionItem.value,
              cellValue: row[conditionItem.prop]
            })
          }
        })

        // 4. 应用最终确定的样式
        this.applyFinalStyles(row, appliedStyles)

      } catch (error) {
        console.error('处理所有条件出错:', error, { item, row, typeName })
      }

      return finalColor
    },

    // 应用最终样式
    applyFinalStyles(row, appliedStyles) {
      try {
        if (appliedStyles.rowbackground) {
          this.$set(row, 'rowbackground', appliedStyles.rowbackground)
        }
        if (appliedStyles.rowfont) {
          this.$set(row, 'rowfont', appliedStyles.rowfont)
        }
        if (appliedStyles.cellfont) {
          this.$set(row, 'cellfont', appliedStyles.cellfont)
        }
        if (appliedStyles.cellbackground) {
          this.$set(row, 'cellbackground', appliedStyles.cellbackground)
        }
      } catch (error) {
        console.error('应用最终样式出错:', error, { row, appliedStyles })
      }
    },

    // 获取表格tab项条件控件 - 多条件处理（保留兼容性）
    getEditableTabsFormJSON(color, item, row, typeName = 'cellbackground') {
      // 重定向到新的统一处理方法
      return this.processAllConditions(item, row, typeName)
    },
    getbackColor(item, row) {
      try {
        // 验证输入参数
        if (!this.validateConditionParams(item, row)) {
          return null
        }

        // 使用统一的多条件处理逻辑
        return this.processAllConditions(item, row, 'cellbackground')
      } catch (error) {
        console.error('获取背景颜色出错:', error, { item, row })
        return null
      }
    },

    getColorByType(item, row, typeName = 'cellbackground') {
      // 条件类型定义:
      // 1: 大于, 2: 小于, 3: 等于, 4: 包含
      // 5: 大于等于, 6: 小于等于, 7: 不等于, 8: 不包含
      let color = null

      // 参数验证
      if (!item || !row || !item.prop) {
        console.warn('getColorByType: 缺少必要参数', { item, row, typeName })
        return color
      }

      // 条件和值必须都存在
      if (!item.condition || item.value === undefined || item.value === null) {
        return color
      }

      try {
        const cellValue = row[item.prop]
        const conditionValue = item.value
        let conditionMet = false

        // 数据类型检查和条件判断
        switch (parseInt(item.condition)) {
          case 1: // 大于
            if (this.isNumeric(cellValue) && this.isNumeric(conditionValue)) {
              conditionMet = parseFloat(cellValue) > parseFloat(conditionValue)
              console.log("大于判断", cellValue, ">", conditionValue, "结果:", conditionMet)
            }
            break

          case 2: // 小于
            if (this.isNumeric(cellValue) && this.isNumeric(conditionValue)) {
              conditionMet = parseFloat(cellValue) < parseFloat(conditionValue)
              console.log("小于判断", cellValue, "<", conditionValue, "结果:", conditionMet)
            }
            break

          case 3: // 等于
            conditionMet = String(cellValue) === String(conditionValue)
            console.log("等于判断", cellValue, "===", conditionValue, "结果:", conditionMet)
            break

          case 4: // 包含
            if (cellValue && typeof cellValue === 'string' && conditionValue) {
              conditionMet = cellValue.includes(String(conditionValue))
              console.log("包含判断", cellValue, "includes", conditionValue, "结果:", conditionMet)
            }
            break

          case 5: // 大于等于
            if (this.isNumeric(cellValue) && this.isNumeric(conditionValue)) {
              conditionMet = parseFloat(cellValue) >= parseFloat(conditionValue)
              console.log("大于等于判断", cellValue, ">=", conditionValue, "结果:", conditionMet)
            }
            break

          case 6: // 小于等于
            if (this.isNumeric(cellValue) && this.isNumeric(conditionValue)) {
              conditionMet = parseFloat(cellValue) <= parseFloat(conditionValue)
              console.log("小于等于判断", cellValue, "<=", conditionValue, "结果:", conditionMet)
            }
            break

          case 7: // 不等于
            conditionMet = String(cellValue) !== String(conditionValue)
            console.log("不等于判断", cellValue, "!==", conditionValue, "结果:", conditionMet)
            break

          case 8: // 不包含
            if (cellValue && typeof cellValue === 'string' && conditionValue) {
              conditionMet = !cellValue.includes(String(conditionValue))
              console.log("不包含判断", cellValue, "not includes", conditionValue, "结果:", conditionMet)
            } else if (!cellValue || !conditionValue) {
              // 如果值为空，认为不包含
              conditionMet = true
            }
            break

          default:
            console.warn('未知的条件类型:', item.condition)
            return color
        }

        // 如果条件满足，应用样式
        if (conditionMet) {
          color = item[typeName]
          this.applyConditionalStyles(row, item)
        }

      } catch (error) {
        console.error('条件判断出错:', error, { item, row, typeName })
      }

      return color
    },

    // 数值检查辅助方法
    isNumeric(value) {
      if (value === null || value === undefined || value === '') {
        return false
      }
      return !isNaN(value) && !isNaN(parseFloat(value))
    },

    // 应用条件样式的辅助方法
    applyConditionalStyles(row, item) {
      try {
        if (item.rowbackground) {
          this.$set(row, 'rowbackground', item.rowbackground)
        }
        if (item.rowfont) {
          this.$set(row, 'rowfont', item.rowfont)
        }
        if (item.cellfont) {
          this.$set(row, 'cellfont', item.cellfont)
        }
        if (item.cellbackground) {
          this.$set(row, 'cellbackground', item.cellbackground)
        }
      } catch (error) {
        console.error('应用条件样式出错:', error, { row, item })
      }
    },

    // 验证条件参数
    validateConditionParams(item, row) {
      if (!item) {
        console.warn('条件验证失败: item 参数为空')
        return false
      }

      if (!row) {
        console.warn('条件验证失败: row 参数为空')
        return false
      }

      if (!item.prop) {
        console.warn('条件验证失败: 缺少 prop 属性', item)
        return false
      }

      return true
    },

    // 验证条件配置
    validateConditionConfig(item) {
      const validConditions = [1, 2, 3, 4, 5, 6, 7, 8]

      if (!item.condition) {
        return { valid: false, message: '缺少条件类型' }
      }

      if (!validConditions.includes(parseInt(item.condition))) {
        return { valid: false, message: `无效的条件类型: ${item.condition}` }
      }

      if (item.value === undefined || item.value === null) {
        return { valid: false, message: '缺少条件值' }
      }

      // 特殊验证：包含和不包含条件需要字符串类型的值
      if ([4, 8].includes(parseInt(item.condition))) {
        if (typeof item.value !== 'string' && item.value !== '') {
          return { valid: false, message: '包含/不包含条件需要字符串类型的值' }
        }
      }

      // 特殊验证：数值比较条件需要数值类型的值
      if ([1, 2, 5, 6].includes(parseInt(item.condition))) {
        if (!this.isNumeric(item.value)) {
          return { valid: false, message: '数值比较条件需要数值类型的值' }
        }
      }

      return { valid: true, message: '配置有效' }
    },

    getFormatter(item, row) {
      // 创建行数据的副本，避免格式化函数污染原始数据
      const rowCopy = { ...row }
      const result = getFunction(item.formatter)(item, rowCopy)

      // 如果格式化函数设置了 font_Color，将其存储到列特定的属性中
      if (rowCopy.font_Color && rowCopy.font_Color !== row.font_Color) {
        // 使用列的 prop 作为键，存储列特定的颜色
        if (!row._columnColors) {
          this.$set(row, '_columnColors', {})
        }
        this.$set(row._columnColors, item.prop, rowCopy.font_Color)
      }

      return result
    },
    handleMouseOver() {
      clearInterval(this.scrollCheck);
    },
    handleMouseLeave() {
      this.setTime()
    },
    cellClick(row, column, cell, event) {
      this.updateClick(row);
      this.clickFormatter && this.clickFormatter({
        type: column,
        item: row,
        data: this.dataChart
      }, this.getItemRefs());
    },
    // 延时执行
    sleep(time = 1000) {
        const promise = new Promise((resolve) => {
            setTimeout(() => {
                resolve(true);
            }, time);
        });
        return promise;
    },
   async setTime() {
        //延时执行
        await this.sleep(3000)
      clearInterval(this.scrollCheck);
      this.headerHeight = this.$refs.table.$refs.headerWrapper ? parseInt(this.$refs.table.$refs.headerWrapper.clientHeight) : 0
      const table = this.$refs.table
      const divData = table.bodyWrapper
      // 滚动速度
      const speed = this.scrollSpeed
      // 第一行，停顿几秒
      let top = 0
    
      // 开启滚动
      if (this.scroll) {
        this.scrollCheck = setInterval(() => {
          top = top + speed
          divData.scrollTop += speed;
          let h1= divData.clientHeight + divData.scrollTop
          let h2= divData.scrollHeight
          if (h1+10 >= h2) {
            // console.log('h1 >= h2 reset scrollTop=0')
              // 最后一行，停顿几秒
              clearInterval(this.scrollCheck);
              setTimeout(async() => {
                divData.scrollTop = 0
                await this.setTime()
              }, 3000)
          }
        
          if (top >= this.cellHeight && this.scrollTime) {
            divData.scrollTop = divData.scrollTop - (top - this.cellHeight)
            clearInterval(this.scrollCheck);
            setTimeout(async() => {
              await this.setTime()
              //滚动间隔
            }, this.scrollTime)
          }
        }, 20)
      } else {
        divData.scrollTop = 0
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      //console.warn("cellStyle row?.rowbackground:",row?.rowbackground)
      let _defaultColor = row?.rowfont ? row?.rowfont : this.option.bodyColor

      // 检查是否有列特定的颜色设置
      if (row._columnColors && row._columnColors[column.property]) {
        _defaultColor = row._columnColors[column.property]
      } else if (!!row["font_Color"]) {
        // 保持向后兼容性：如果没有列特定颜色，使用全局 font_Color
        // 但只在没有设置列特定颜色的情况下使用
        if (!row._columnColors || Object.keys(row._columnColors).length === 0) {
          _defaultColor = row["font_Color"]
        }
      }

      return {
        padding: 0,
        height: this.setPx(this.cellHeight),
        fontSize: this.setPx(this.option.bodyFontSize),
        color: _defaultColor,
        textAlign: column.type == 'index' ? 'center' : this.option.bodyTextAlign,
        backgroundColor: row?.rowbackground ? row?.rowbackground : rowIndex % 2 == 0 ? this.option.othColor : this.option.nthColor,

      }
    },
    rowStyle({ row, column, rowIndex }) {
      console.warn("rowStyle row?.rowbackground:",row?.rowbackground)
      let _defaultColor = row?.rowfont ? row?.rowfont : this.option.bodyColor
      return {
        backgroundColor: row?.rowbackground ? row?.rowbackground : rowIndex % 2 == 0 ? this.option.othColor : this.option.nthColor,
        color: _defaultColor,
      }
    },
    headerRowStyle() {
      let _headerRowStyle = {
        backgroundColor: this.option.headerBackground
      }
      if(this.option.headerColHeight){
        _headerRowStyle.height = this.setPx(this.option.headerColHeight)//NEW 列头高度设置
      }
      return _headerRowStyle
    },
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      let _headerCellStyle ={
        fontSize: this.setPx(this.option.headerFontSize),
        backgroundColor: this.option.headerBackground,
        color: this.option.headerColor,
        textAlign: column.type == 'index' ? 'center' : this.option.headerTextAlign
      }
      if(this.option.headerFontSize){
        // 特殊样式处理 列头高度设置
        this.tableCellStyle =`<style type="text/css">
            .el-table .cell{
                  line-height: ${this.option.headerFontSize+3}px !important;
                }
            </style>`
      }
     
      return _headerCellStyle
    }
  }
});
</script>
<style lang="scss" scoped>
.rounded-table {
  border-radius: 15px;
  overflow: hidden;
}
.cell-word-break {
  white-space: pre-line;
  word-break: break-all;
}
/* 添加一个自定义类来控制表头高度 el-table__header-wrapper el-table__header*/ 
// ::v-deep .custom-table-header {
//   .el-table__header  tr {
//     height: 150px; /* 你想要的表头高度 */ 
//   }
// }
 // 默认情况下隐藏边框
//  ::v-deep .el-table:not([border]) td, .el-table:not([border]) th.is-leaf{
//    border-bottom: 1px solid #EBEEF5 !important;
//  }


</style>
