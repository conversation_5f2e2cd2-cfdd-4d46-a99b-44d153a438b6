import { setPx, validatenull, addParam, loadFont } from "./util";
import config from "./config";
import commonOption from "./option";
import crypto from "@/utils/crypto";
import { getFunction, funEval, uuid } from "@/utils/utils";
import { GetListByDataSetId } from "@/api/kanban";
import COMMON from "@/config";
import cloneDeep from 'clone-deep'
const echartTypeList = ["bar","pie","line","flop","progress","wordcloud","gauge","datav","img","video","swiper","clapper","funnel","rectangle","text","pictorialbar"] // 使用X,Y轴模型的图表类型
export default (() => {
  return {
    props: {
      stylesFormatter: Function,
      dataFormatter: Function,
      titleFormatter: Function,
      labelFormatter: Function,
      clickFormatter: Function,
      sqlFormatter: Function,
      recordFormatter: Function,
      formatter: Function,
      echartFormatter: Function,
      //echartFormatterStr: String,
      dataQueryType: String,
      dataQuery: String,
      dataHeader: String,
      fontFamily: String,
      width: {
        type: [Number, String],
        default: 600,
      },
      height: {
        type: [Number, String],
        default: 600,
      },
      theme: {
        type: String,
      },
      globalDataConfig: {
        type: Object,
        default: () => {
          return {};
        },
      },
      child: {
        type: Object,
        default: () => {
          return {};
        },
      },
      record: {
        type: String,
      },

      sql: {
        type: String,
      },
      time: {
        type: Number,
        default: 0,
      },

      url: {
        type: String,
      },
      wsUrl: {
        type: String,
      },
      disabled: {
        type: Boolean,
        default: true,
      },
      dataType: {
        type: Number,
        default: 0,
      },
      dataMethod: {
        type: String,
        default: "get",
      },
      id: {
        type: String,
        default: "main_" + uuid(),
      },
      data: {
        type: [Object, String, Array],
      },
      component: {
        type: Object,
        default: () => {
          return {};
        },
      },
      option: {
        type: Object,
        default: () => {
          return {};
        },
      },
      //数据集id

      dataSet: {
        type: Number,
      },
      paramters: {
        type: Array,
      },
      headers: {
        type: Array,
      },
      dataMulModelY: {
        type: Array,
      },
      dataModelX: {
        type: String,
      },
      dataModelY: {
        type: String,
      },
      // 其它动态配置参数 by andy 2024-10-24
      dataModelOthers: {
        type: Object,
        default: () => {
          return {
            test: 111,
          };
        },
      },
    },
    mixins: [commonOption],
    data() {
      return {
        firstDataHasLoad: false, // 第一条API 是否已经加载
        headerHeight: "",
        checkChart: "",
        globalTimerCheck: "",
        myChart: "",
        dataChart: [],
        dataAxios: {},
        dataParams: {},
        wsClient: {},
        styles: {},
        appendCheck: {},
        appendObj: {},
        appendList: [],
        className: "",
      };
    },
    watch: {
      $route: {
        immediate: false,
        handler() {
           console.log("======route change=========this.init()()");
           this.init();
        },
      },
      data() {
        this.updateData();
      },
      dataAppend(val) {
        this.appendObj = {};
        this.appendList = [];
        if (!val) {
          this.appendCheck = clearInterval(this.appendCheck);
        } else {
          this.dataChart = [];
        }
        this.updateData();
      },
      echartFormatter() {
        console.log("======echartFormatter=88888========");
        clearTimeout(this.timer_echartFormatter);
        this.timer_echartFormatter = setTimeout(() => {
          this.updateChart();
        }, 1000);
        //this.updateChart();
      },
      width() {
        this.$nextTick(() => {
          this.updateChart();
        });
      },
      height() {
        this.$nextTick(() => {
          this.updateChart();
        });
        this.updateChart();
      },
      theme() {
        this.myChart.dispose();
        this.init();
      },
      option: {
        handler() {
          console.log("======option change=========updateChart()");
          //this.updateChart();
        },
        deep: true,
      },
    },
    computed: {
      count() {
        return this.option.count;
      },
      dataAppend() {
        return this.option.dataAppend;
      },
      dataChartLen() {
        return (this.dataChart || []).length;
      },
      switchTheme() {
        return this.vaildData(this.option.switchTheme, false);
      },
      name() {
        let className = this.$el.className.split(" ")[0];
        const result = className.replace(config.name, "");
        return result;
      },
      minWidth() {
        const val = this.option.minWidth;
        if (val > this.width) return val;
      },
      styleChartName() {
        const obj = {
          fontFamily: loadFont(this.component.fontFamily),
          width: setPx(this.minWidth || this.width),
          height: setPx(this.height),
          //lineHeight: setPx(this.height),
          opacity: this.component.opacity || 1,
          transform: `scale(${this.component.scale || 1}) perspective(${
            this.component.perspective || 500
          }px) rotateX(${this.component.rotateX || 0}deg) rotateY(${
            this.component.rotateY || 0
          }deg) rotateZ(${this.component.rotateZ || 0}deg)`,
        };
        return obj;
      },
      styleSizeName() {
        return Object.assign(
          {
            width: setPx(this.width),
            height: setPx(this.height),
          },
          (() => {
            if (this.minWidth) {
              return {
                overflowX: "auto",
                overflowY: "hidden",
              };
            }
            return {};
          })(),
          this.styles
        );
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        //debugger
        this.className = `animated ${this.component.animated || ""}`;
        const main = this.$refs[this.id];
        if (main) {
          // 判断是否图表去初始化
          const isChart = config.echart.includes(this.name);
          if (isChart) this.myChart = window.echarts.init(main, this.theme);
        }
        this.updateChart();

        setTimeout(() => {
          this.updateData();
        }, 1000);
      },
      getItemRefs() {
        let refList = this.$parent.$parent.$refs;
        let result = {};
        Object.keys(refList).forEach((ele) => {
          if (ele.indexOf(COMMON.NAME) !== -1) {
            result[ele.replace(COMMON.NAME, "")] = refList[ele][0];
          }
        });
        return result;
      },
      // 占位方法，mixins中具体实现其方法内容，类似继承
      updateChart() {},
      updateClick(params) {
        let refList = this.getItemRefs();
        let indexList = this.child.index;
        let indexName = this.child.paramName;
        let indexValue = this.child.paramValue || "value";
        if (validatenull(indexName) && validatenull(indexList)) return;
        let p = {};
        p[indexName] = params[indexValue];
        Object.keys(refList).forEach((ele) => {
          if (indexList.includes(ele)) refList[ele].updateData(p);
        });
      },
      updateAppend(result) {
        if (this.validatenull(this.appendObj)) {
          this.appendList = result;
          this.appendObj = result[0];
        } else {
          let appendList = [];
          for (let i = 0; i < result.length; i++) {
            const ele = result[i];
            if (ele.id === this.appendObj.id) break;
            else appendList.push(ele);
          }
          this.appendObj = result[0];
          appendList.reverse().forEach((ele) => {
            this.appendList.unshift(ele);
          });
        }
        if (this.validatenull(this.appendCheck)) {
          this.appendCheck = setInterval(() => {
            let length = this.appendList.length - 1;
            if (length >= 0) {
              let obj = this.appendList.splice(length, 1)[0];
              this.dataChart.unshift(obj);
              let len = this.dataChart.length;
              if (len > this.count) {
                this.appendList.splice(len - 1, 1);
              }
            }
          }, 2000);
        }
      },
      sleep(seconds) {
        return new Promise((resolve) => setTimeout(resolve, seconds));
      },
      delay(fn, seconds, ...args) {
        return new Promise((resolve) =>
          setTimeout(() => {
            // console.log(args);
            Promise.resolve(fn(...args)).then(resolve);
          }, seconds)
        );
      },
      getFirstDataByType(dataList) {
        let _componentName = this.$attrs.name;
        let _formatData = cloneDeep(dataList);
        // 仪表盘 进度条
        if (["gauge", "progress"].includes(this.name)) {
          //debugger
          if (dataList && Array.isArray(dataList) && dataList.length > 0) {
            _formatData = dataList[0];
          }
        } else if (["flop"].includes(this.name)) {
          // "flop" 包含 颜色块 //翻牌器
          if (_componentName.includes("翻牌器")) {
            if (dataList && Array.isArray(dataList) && dataList.length > 0) {
              _formatData = dataList[0];
            }
          }
        }
        return _formatData;
      },
      // 绑定事件-【迁移方法-20240923】
      bindEvent() {
        if (this.myChart) {
          [
            {
              name: "click",
              event: "handleClick",
            },
            {
              name: "dblclick",
              event: "handleDblClick",
            },
            {
              name: "mouseover",
              event: "handleMouseEnter",
            },
            {
              name: "mouseout",
              event: "handleMouseLeave",
            },
          ].forEach((ele) => {
            try {
              this.myChart.off(ele.name);
              this.myChart.on(ele.name, (e) => this[ele.event](e, e.dataIndex));
            } catch (error) {}
          });
        }
        if (typeof this.stylesFormatter === "function") {
          this.styles =
            this.stylesFormatter(
              this.dataChart,
              this.dataParams,
              this.getItemRefs()
            ) || {};
        }
      },
      // 更新数据核心方法
      updateData(defaultParams = {}, otherOption = {}) {
        //debugger
        console.log("=========更新数据核心方法==========");
        console.log("this.dataType:", this.dataType);
        let record,
          key = false;
        let isRecord = this.dataType === 4;
        let isCustom = this.dataType === 5;
        let isGlobal = this.dataType === 6;
        this.dataParams = Object.assign(this.dataParams, defaultParams);
        let _self = this;
        let cacheDataTimer = {
          key: this.dataSet,
          value: 1,
        };

        // 设置缓存数据set_globalDataTimer
        this.$store.commit("set_globalDataTimer", cacheDataTimer);
        console.warn("set_globalDataTimer", cacheDataTimer);

        let globalTimer = 0;
        try {
          globalTimer = this.globalDataConfig.globalDataSource[0].time;
        } catch (error) {
          globalTimer = 0;
        }
        console.log("globalTimer:", globalTimer);
        return new Promise((resolve, reject) => {
          this.resetData && this.resetData();
          if (key) return;
          key = true;
          let safe = this;
          const formatter = (data, params) => {
            console.log("====updateData=====formatter==========");
            if (isRecord || isCustom || isGlobal) {
              //debugger
              const dataFormatter = getFunction(safe.dataFormatter);
              if (typeof dataFormatter == "function") {
                data = dataFormatter(data);
              }
            }
            if (typeof this.dataFormatter === "function") {
              try {
                data = this.dataFormatter(data, params, this.getItemRefs());
              } catch (error) {
                console.error("dataFormatter 错误：", error);
              }
            }
            return data;
          };
          const getData = (newTimerIndex = 0) => {
            console.log("====updateData=====getData==========");
            safe = record || this;
            key = false;
            let isApi = safe.dataType === 1;
            let isSql = safe.dataType === 2;
            let isWs = safe.dataType === 3;
            let isCustom = safe.dataType === 5;
            let isGlobalDataSource = safe.dataType === 6;
            this.closeClient();
            const bindEvent = (originData = []) => {
              //debugger
              this.updateChart();
              if (this.myChart) this.bindClick();
              if (typeof this.stylesFormatter === "function") {
                this.styles =
                  this.stylesFormatter(
                    this.dataChart,
                    this.dataParams,
                    this.getItemRefs()
                  ) || {};
              }
              // 根据类型 判断是否需要返回数组的一个值
              this.dataChart = this.getFirstDataByType(this.dataChart);
              resolve(this.dataChart);
            };
            if (isApi) {
              let arr = JSON.parse(crypto.decrypt(safe.$attrs.isApi));
              let url = safe.url;

              if (this.validatenull(url)) return;

              let dataQuery = getFunction(safe.dataQuery || arr.dataQuery);
              dataQuery =
                (typeof dataQuery === "function" && dataQuery(url)) || {};
              let dataHeader = getFunction(safe.dataHeader);
              dataHeader =
                (typeof dataHeader === "function" && dataHeader(url)) || {};
              let params = Object.assign(dataQuery, this.dataParams);

              let axiosParams = {};
              if (["post", "put"].includes(safe.dataMethod)) {
                axiosParams.data = params;
              } else if (["get", "delete"].includes(safe.dataMethod)) {
                axiosParams.params = params;
              }

              this.$axios({
                ...{
                  method: safe.dataMethod,
                  url: url,
                  headers: dataHeader,
                },
                ...axiosParams,
              }).then((res) => {
                let result;

                res = res.data;
                if (res.code === 200 && res.data.Success) {
                  if (typeof this.dataFormatter === "function") {
                    result = res.data.Datas;
                    //debugger
                    this.dataChart = formatter(result, this.dataParams);
                    bindEvent();
                  } else {
                    this.$eventBus.$emit("result", res.data.Datas);
                  }
                }
              });
            } else if (isWs) {
              let url = safe.wsUrl;
              if (this.validatenull(url)) return;
              let dataQuery = getFunction(safe.dataQuery);
              dataQuery =
                (typeof dataQuery === "function" && dataQuery(url)) || {};
              let params = Object.assign(dataQuery, this.dataParams);
              url = url + addParam(params);
              this.wsClient = new WebSocket(url);
              this.wsClient.onmessage = (msgEvent = {}) => {
                let result = JSON.parse(msgEvent.data);
                //debugger
                this.dataChart = formatter(result, this.dataParams);
                bindEvent();
              };
            } else if (isSql) {
              let sql = JSON.parse(crypto.decrypt(safe.sql));
              let data, result;
              try {
                sql.sql = funEval(sql.sql)(this.dataParams);
                data = crypto.encrypt(JSON.stringify(sql));
              } catch (error) {
                data = safe.sql;
              }
              this.sqlFormatter(data).then((res) => {
                result = res.data.data;
                //debugger
                this.dataChart = formatter(result, this.dataParams);
                bindEvent();
              });
              //新增自定义
            } else if (isCustom) {
              console.log("====updateData=====getData======isCustom====");
              let result;
              let arr = JSON.parse(crypto.decrypt(safe.$attrs.isCustom));
              // console.log('====paramters====:',arr)
              let paramters = arr.paramters;
              const params = {
                Parameter: {},
                Id: safe.dataSet,
              };

              paramters?.map((itm) => {
                if (itm.key != "") {
                  params.Parameter[itm.key] = itm.value;
                }
              });

              GetListByDataSetId(params)
                .then((res) => {
                  //debugger
                  res = res.data;
                  if (res.code === 200 && res.data.Success) {
                    if (typeof _self.dataFormatter === "function") {
                      let _data = res.data.Datas;
                      //debugger
                      if (echartTypeList.includes(safe.name)) {
                        if (typeof _data === "string") {
                          result = this.runevel(JSON.parse(res.data.Datas));
                        } else {
                          result = this.runevel(res.data.Datas);
                        }
                      } else {
                        result = _data;
                      }
                    } else {
                      //debugger
                      let _data = res.data.Datas;
                      if (echartTypeList.includes(safe.name)) {
                        if (typeof _data === "string") {
                          result = this.runevel(JSON.parse(res.data.Datas));
                        } else {
                          result = this.runevel(res.data.Datas);
                        }
                      } else {
                        result = _data;
                      }
                    }
                    //debugger
                    _self.dataChart = formatter(result, _self.dataParams);
                    bindEvent();
                  }
                })
                .catch((error) => {
                  //debugger
                  if (window.location.href.includes("build")) {
                    this.$message({
                      message: error,
                      type: "error",
                    });
                  }
                });
            } else if (isGlobalDataSource) {
              if (this.$store.state.isGlobalfetching) {
                //全局数据源正在获取中... 跳过
                console.log("全局数据源正在获取中... 跳过");
                return;
              }
              let result;
              let arr = JSON.parse(crypto.decrypt(safe.$attrs.isCustom));
              // debugger
              let paramters = arr.paramters;
              const params = {
                Parameter: {},
                Id: safe.dataSet,
              };
              paramters?.map((itm) => {
                if (itm.key != "") {
                  params.Parameter[itm.key] = itm.value;
                }
              });
              // 获取缓存数据dataSetId为key
              let getDataCache =
                _self.$store.state.globalDataSource.value[safe.dataSet];

              const now = Date.now();
              if (
                getDataCache &&
                _self.$store.state.lasGlobaltFetchTime &&
                now - _self.$store.state.lasGlobaltFetchTime < globalTimer
              ) {
                _self.dataChart = formatter(getDataCache, _self.dataParams);
                bindEvent(_self.dataChart);
                console.log("从全局缓存中获取数据:", [getDataCache[0]]);
                //    debugger
                //   if(_self.dataParams && Object.keys(_self.dataParams).length > 0){
                //     _self.dataChart = formatter(getDataCache, _self.dataParams);
                //   }else{
                //     _self.dataChart = getDataCache;
                //  }
                //   bindEvent( _self.dataChart);
                return;
              } else {
                let cacheData = {
                  key: safe.dataSet,
                  value: null,
                };
                console.log("清除全局缓存"); //不能清空，否则出现短暂空白界面
                this.$store.commit("set_globalDataSource", cacheData);
              }
              _self.$store.commit("set_isGlobalfetching", true);
              GetListByDataSetId(params)
                .then((res) => {
                  res = res.data;
                  if (res.code === 200 && res.data.Success) {
                    //debugger
                    this.firstDataHasLoad = true;
                    if (!isGlobalDataSource) {
                      if (typeof _self.dataFormatter === "function") {
                        let _data = res.data.Datas;
                        if (echartTypeList.includes(safe.name)) {
                          if (typeof _data === "string") {
                            result = this.runevel(JSON.parse(res.data.Datas));
                          } else {
                            result = this.runevel(res.data.Datas);
                          }
                        } else {
                          result = _data;
                        }
                      } else {
                        let _data = res.data.Datas;
                        if (echartTypeList.includes(safe.name)) {
                          if (typeof _data === "string") {
                            result = this.runevel(JSON.parse(res.data.Datas));
                          } else {
                            result = this.runevel(res.data.Datas);
                          }
                        } else {
                          result = _data;
                        }
                      }
                    } else {
                      result = res.data.Datas;
                    }

                    let cacheData = {
                      key: safe.dataSet,
                      value: result,
                    };
                    console.log("设置全局缓存:", cacheData);
                    // 设置缓存数据
                    _self.$store.commit("set_lasGlobaltFetchTime", now);
                    _self.$store.commit("set_globalDataSource", cacheData);
                    _self.$store.commit("set_isGlobalfetching", false);
                    //  if(_self.dataParams && Object.keys(_self.dataParams).length > 0){
                    //     _self.dataChart = formatter(result, _self.dataParams);
                    //   }else{
                    //     _self.dataChart = result;
                    //  }
                    _self.dataChart = formatter(result, _self.dataParams);
                    bindEvent(_self.dataChart);
                  }
                  _self.$store.commit("set_isGlobalfetching", false);
                })
                .catch((error) => {
                  _self.$store.commit("set_isGlobalfetching", false);
                  if (window.location.href.includes("build")) {
                    this.$message({
                      message: error,
                      type: "error",
                    });
                  }
                });
            } else {
              let result = safe.data;
              if (isRecord) {
                result = funEval(result);
              }

              if (this.dataParams && Object.keys(this.dataParams).length > 0) {
                this.dataChart = formatter(result, this.dataParams);
              } else {
                this.dataChart = result;
              }
              bindEvent(this.dataChart);
            }
          };

          const sendData = () => {
            let _self = this;
            this.$nextTick(() => {
              let timerIndex = this.$store.state.globalDataTimer.value[
                this.dataSet
              ]
                ? this.$store.state.globalDataTimer.value[this.dataSet]
                : 1;
              console.log("timerIndex", timerIndex);
              getData();
              if (isGlobal) {
                clearInterval(this.globalTimerCheck);
              } else {
                clearInterval(this.checkChart);
              }

              // 获取大于1000的随机数
              let randomNumber = Math.floor(
                Math.random() * 10 + 500 * timerIndex
              );
              if (isGlobal) {
                //globalTimer 不为0 表示定时间隔获取最新数据
                if (globalTimer !== 0) {
                  this.globalTimerCheck = setInterval(
                    () => {
                      getData(timerIndex);
                    },
                    globalTimer + randomNumber,
                    timerIndex
                  );
                }
              } else {
                if (this.time !== 0) {
                  this.checkChart = setInterval(() => {
                    console.log("=========getData===55555====");
                    getData();
                  }, this.time);
                }
              }
            });
          };
          if (isRecord) {
            this.recordFormatter(this.record).then((res) => {
              const data = res.data.data;
              record = {
                ...data,
                sql: data.data,
              };
              sendData();
            });
          } else {
            sendData();
          }
        });
      },

      runevel(datas) {
        //debugger
        let _componentName = this.$attrs.name;
        console.log("runevel=========_componentName====", _componentName);
        let _self = this;
        if (!this.dataModelY) {
          // 有些只需要一个值即可，|| !this.dataModelX
          return datas;
        }
        let _resFormatData = null;
        if (Object.prototype.toString.call(datas) === "[object Array]") {
          _resFormatData = [];
          switch (this.name) {
            case "text":
            case "clapper":
            case "swiper":
            case "video":
            case "img":
              datas.forEach((ele) => {
                _resFormatData.push({
                  value: ele[this.dataModelY],
                });
              });
              break;
            //饼图，字符串云
            case "datav":
              if (
                _componentName.includes("水位图") ||
                _componentName.includes("进度池")
              ) {
                datas.forEach((ele) => {
                  _resFormatData.push({
                    value: ele[this.dataModelY], //
                  });
                });
              }
              //滚动排名 胶囊排名 锥形柱图
              if (
                _componentName.includes("动态环图") ||
                _componentName.includes("滚动排名") ||
                _componentName.includes("胶囊排名") ||
                _componentName.includes("锥形柱图")
              ) {
                if (!this.dataModelX) {
                  // 有些只需要一个值即可，|| !this.dataModelX
                  return datas;
                }
                datas.forEach((ele) => {
                  _resFormatData.push({
                    name: ele[this.dataModelX],
                    value: Number(ele[this.dataModelY]), //
                  });
                });
              }
              break;
            //饼图，字符串云
            case "pictorialbar":
            case "pie":
            case "wordcloud":
            case "funnel": // 漏斗图
            case "rectangle": // 矩形图
              if (!this.dataModelX) {
                // 有些只需要一个值即可，|| !this.dataModelX
                return datas;
              }
              datas.forEach((ele) => {
                _resFormatData.push({
                  name: ele[this.dataModelX],
                  value: ele[this.dataModelY],
                });
              });

              break;
            // 仪表盘
            case "gauge":
              datas.forEach((ele) => {
                _resFormatData.push({
                  value: ele[this.dataModelY],
                });
              });

              break;
            // 进度条
            case "progress":
              if (!this.dataModelX) {
                // 有些只需要一个值即可，|| !this.dataModelX
                return datas;
              }
              datas.forEach((ele) => {
                _resFormatData.push({
                  // "label": "进度",// 描述中赋值
                  data: ele[this.dataModelX],
                  value: ele[this.dataModelY],
                });
              });

              break;
            case "flop":
              if (_componentName.includes("翻牌器")) {
                datas.forEach((ele) => {
                  _resFormatData.push({
                    value: ele[this.dataModelY] + "", // 转为字符串
                  });
                });
                break;
              }
              // 颜色块，默认格式
              let _defaultColorList = [
                "#67C23A",
                "#409EFF",
                "#E6A23C",
                "#F56C6C",
                "#7232dd",
                "blue",
              ];
              datas.forEach((ele, index) => {
                _resFormatData.push({
                  backgroundColor:
                    this.getColor(index) || _defaultColorList[index],
                  prefixText: ele[this.dataModelX],
                  value: ele[this.dataModelY],
                  suffixText: "",
                });
              });
              break;
            // 柱图，折线图 默认返回格式
            default:
              //////////////多Y轴模型/////////////////////
              let _defaultSeriesItem = {
                name: this.getValueByField_InBarColor(0, "desc"),
                data: [],
              };
              let _defaultSeries = [cloneDeep(_defaultSeriesItem)];
              if (_self.dataMulModelY && _self.dataMulModelY.length > 0) {
                _self.dataMulModelY.forEach((ele, index) => {
                  let newItem = cloneDeep(_defaultSeriesItem);
                  newItem.name = this.getValueByField_InBarColor(
                    index + 1,
                    "desc"
                  );
                  _defaultSeries.push(newItem);
                });
              }
              /////////////多Y轴模型//////////////////////
              _resFormatData = {
                categories: [],
                series: _defaultSeries,
              };
              datas.forEach((ele) => {
                _resFormatData.categories.push(ele[_self.dataModelX]);
                _resFormatData.series[0].data.push(ele[_self.dataModelY]);
                /////////////多Y轴模型//////////////////////
                if (_self.dataMulModelY && _self.dataMulModelY.length > 0) {
                  _self.dataMulModelY.forEach((modelYItem, index) => {
                    _resFormatData.series[index + 1].data.push(ele[modelYItem]);
                  });
                }
                /////////////多Y轴模型//////////////////////
              });
              break;
          }
        } else if (
          Object.prototype.toString.call(datas) === "[object Object]"
        ) {
          _resFormatData = {};
        } else {
          _resFormatData = [];
        }
        // debugger
        return _resFormatData;
      },

      // 绑定点击事件
      bindClick() {
        this.myChart.off("click");
        this.myChart.on("click", (e) => {
          this.updateClick(this.dataChart[e.dataIndex] || e);
          this.clickFormatter &&
            this.clickFormatter(
              Object.assign(e, {
                data: this.dataChart,
              }),
              this.getItemRefs()
            );
        });
      },
      // 下面俩都是chart的公共的方法,就放这里面共用
      getColor(index, first) {
        const barColor = this.option.barColor || [];
        if (barColor[index]) {
          const color1 = barColor[index].color1;
          const color2 = barColor[index].color2;
          const postion = (barColor[index].postion || 0.9) * 0.01;
          if (first) return color1;
          if (color2) {
            return {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: color1, // 0% 处的颜色
                },
                {
                  offset: postion,
                  color: color2, // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            };
          }
          return color1;
        }
      },
      // 下面俩都是chart的公共的方法,就放这里面共用
      getValueByField_InBarColor(index, fieldName) {
        try {
          const barColor = this.option.barColor || [];
          if (barColor[index]) {
            const val = barColor[index][fieldName];
            return val;
          } else {
            return "";
          }
        } catch (error) {
          return "";
        }
      },
      getHasProp(isHas, propObj, staticObj = {}) {
        return Object.assign(
          (() => {
            return isHas ? propObj : {};
          })(),
          staticObj
        );
      },
      closeClient() {
        this.wsClient.close && this.wsClient.close();
      },
    },
    beforeDestroy() {
      clearInterval(this.checkChart);
      clearInterval(this.globalTimerCheck);
      this.closeClient();
    },
  };
})();
