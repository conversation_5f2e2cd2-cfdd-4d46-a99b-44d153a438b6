<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格格式化函数修复测试</title>
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .example-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .example-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #303133;
            border-bottom: 2px solid #409eff;
            padding-bottom: 8px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .success {
            background-color: #f0f9ff;
            border-left: 4px solid #67c23a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格格式化函数修复测试</h1>
        <p>测试您原始的格式化函数代码，现在应该只影响当前列，不会影响其他列。</p>
        
        <div class="example-section success">
            <div class="example-title">✅ 修复后效果：您的原始代码现在可以正常工作</div>
            <div class="code-block">
// 您的原始格式化函数（现在已修复）
(name,data)=>{
    if(data["type1"].includes("1")){
        data.font_Color="red"
    } 
    if(data["type1"].includes("2")){ 
        data.font_Color="green" 
    } 
    return data["type1"] 
}
            </div>
            <div id="fixedTable" style="height: 400px; margin: 20px 0;"></div>
        </div>

        <div class="example-section">
            <div class="example-title">🎯 多列测试：验证列之间不会互相影响</div>
            <div id="multiColumnTable" style="height: 400px; margin: 20px 0;"></div>
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <script src="../dist/cdn/element-ui/index.js"></script>
    <script src="../dist/lib/index.umd.min.js"></script>

    <script>
        // 测试数据
        const testData = [
            { id: 1, name: "张三", type1: "类型1", type2: "类型2", status: "正常", score: 85 },
            { id: 2, name: "李四", type1: "类型2", type2: "类型1", status: "异常", score: 92 },
            { id: 3, name: "王五", type1: "类型1类型2", type2: "类型3", status: "正常", score: 78 },
            { id: 4, name: "赵六", type1: "类型3", type2: "类型1类型2", status: "警告", score: 88 },
            { id: 5, name: "钱七", type1: "类型2", type2: "类型1", status: "正常", score: 95 }
        ];

        // 修复后的配置 - 使用您的原始格式化函数
        const fixedConfig = {
            showHeader: true,
            border: true,
            index: true,
            indexWidth: 60,
            headerBackground: "#67c23a",
            headerColor: "#ffffff",
            headerFontSize: 14,
            bodyFontSize: 13,
            column: [
                { label: "姓名", prop: "name", width: 100 },
                { 
                    label: "类型1（您的代码）", 
                    prop: "type1", 
                    width: 150,
                    // 您的原始格式化函数
                    formatter: `(name,data)=>{
                        if(data["type1"].includes("1")){
                            data.font_Color="red"
                        } 
                        if(data["type1"].includes("2")){ 
                            data.font_Color="green" 
                        } 
                        return data["type1"] 
                    }`
                },
                { label: "类型2（不受影响）", prop: "type2", width: 150 },
                { label: "状态（不受影响）", prop: "status", width: 120 },
                { label: "分数（不受影响）", prop: "score", width: 100 }
            ]
        };

        // 多列格式化测试配置
        const multiColumnConfig = {
            showHeader: true,
            border: true,
            index: true,
            indexWidth: 60,
            headerBackground: "#409eff",
            headerColor: "#ffffff",
            headerFontSize: 14,
            bodyFontSize: 13,
            column: [
                { label: "姓名", prop: "name", width: 100 },
                { 
                    label: "类型1（红绿色）", 
                    prop: "type1", 
                    width: 150,
                    formatter: `(name,data)=>{
                        if(data["type1"].includes("1")){
                            data.font_Color="red"
                        } 
                        if(data["type1"].includes("2")){ 
                            data.font_Color="green" 
                        } 
                        return data["type1"] 
                    }`
                },
                { 
                    label: "类型2（蓝橙色）", 
                    prop: "type2", 
                    width: 150,
                    formatter: `(item,row)=>{
                        if(row["type2"].includes("1")){
                            row.font_Color="blue"
                        } 
                        if(row["type2"].includes("2")){ 
                            row.font_Color="orange" 
                        } 
                        return row["type2"] 
                    }`
                },
                { 
                    label: "分数（紫色系）", 
                    prop: "score", 
                    width: 120,
                    formatter: `(item,row)=>{
                        if(row["score"] >= 90){
                            row.font_Color="purple"
                        } else if(row["score"] >= 80){ 
                            row.font_Color="darkviolet" 
                        } 
                        return row["score"] 
                    }`
                },
                { label: "状态（默认色）", prop: "status", width: 120 }
            ]
        };

        // 初始化表格
        new Vue({
            el: '#fixedTable',
            data() {
                return {
                    option: fixedConfig,
                    data: [...testData]
                }
            },
            template: '<dv-table :option="option" :data="data"></dv-table>'
        });

        new Vue({
            el: '#multiColumnTable',
            data() {
                return {
                    option: multiColumnConfig,
                    data: [...testData]
                }
            },
            template: '<dv-table :option="option" :data="data"></dv-table>'
        });
    </script>
</body>
</html>
